logLevel: 5
http:
  addr: 0.0.0.0
  port: 8080
  tls: true
tls:
  cert:  ../../tls.crt 
  key:  ../../tls.key
loki:
  addr: loki.monitoring
  port: 3100
prometheus:
  addr: prometheus-k8s.monitoring
  port: 9090
  promQLTemplates: 
    {
      "cluster_occupied_cpu_core":             "sum(cluster:namespace:pod_cpu:active:kube_pod_container_resource_requests)",
      "cluster_cpu_utilisation":               "avg(instance:node_cpu_utilisation:rate5m)",
      "cluster_occupied_cpu_utilisation":      "sum(cluster:namespace:pod_cpu:active:kube_pod_container_resource_requests) /sum(instance:node_num_cpu:sum) * 100",
      "cluster_total_cpu_core":                "sum(instance:node_num_cpu:sum)",
      "cluster_occupied_mem_utilisation":      "sum(cluster:namespace:pod_memory:active:kube_pod_container_resource_requests) / sum(node_memory_MemTotal_bytes) * 100",
      "cluster_occupied_mem_bytes":            "sum(cluster:namespace:pod_memory:active:kube_pod_container_resource_requests)",
      "cluster_total_mem_bytes":               "sum(node_memory_MemTotal_bytes)",
      "cluster_mem_utilisation":               "avg(instance:node_memory_utilisation:ratio) * 100",
      "cluster_occupied_gpu_num":              "sum(kube_pod_status_phase{phase=\"Running\"} * on (pod) group_left sum(kube_pod_container_resource_requests{resource=~\"nvidia.*\"}) by (pod, resource))",
      "cluster_occupied_gpu_utilisation":      "(sum(kube_pod_status_phase{phase=\"Running\"} * on (pod) group_left sum(kube_pod_container_resource_requests{resource=~\"nvidia.*\"}) by (pod, resource)))/ sum(kube_node_status_capacity{resource=~\"nvidia.*\"}) * 100",
      "cluster_occupied_gpu_type_utilisation": "(sum(kube_pod_status_phase{phase=\"Running\"} * on (pod) group_left sum(kube_pod_container_resource_requests{resource=~\"$5.*\"}) by (pod, resource)))/ sum(kube_node_status_capacity{resource=~\"$5.*\"}) * 100",
      "cluster_occupied_gpu_type_time_hour":   "sum_over_time((sum(kube_pod_container_resource_requests{namespace=\"hero-user\",resource=~\"nvidia.*|huawei.*\"}* on (pod) group_left () sum by (pod) (kube_pod_status_phase{namespace=\"hero-user\",phase=\"Running\"}) > 0) by(resource))[$6:]) * 30 / 3600",
      "cluster_total_gpu_type_num":            "sum(kube_node_status_capacity{resource=~\"nvidia.*\"}) by(resource) > 0",
      "cluster_total_gpu_num":                 "sum(kube_node_status_capacity{resource=~\"nvidia.*\"})",
      "cluster_gpu_utilisation":               "avg(DCGM_FI_DEV_GPU_UTIL)",
      "cluster_occupied_npu_type_num":         "sum(kube_pod_status_phase{phase=\"Running\"} * on (pod) group_left(namespace, resource) sum(kube_pod_container_resource_requests{resource=~\"huawei.*\"}) by (pod, resource)) by (resource)",
      "cluster_occupied_npu_num":              "sum(kube_pod_status_phase{phase=\"Running\"} * on (pod) group_left sum(kube_pod_container_resource_requests{resource=~\"huawei.*\"}) by (pod, resource))",
      "cluster_occupied_npu_utilisation":      "(sum(kube_pod_status_phase{phase=\"Running\"} * on (pod) group_left sum(kube_pod_container_resource_requests{resource=~\"huawei.*\"}) by (pod, resource))) / sum(kube_node_status_capacity{resource=~\"huawei.*\"}) * 100",
      "cluster_occupied_npu_type_utilisation": "(sum(kube_pod_status_phase{phase=\"Running\"} * on (pod) group_left sum(kube_pod_container_resource_requests{resource=~\"$5.*\"}) by (pod, resource)))/ sum(kube_node_status_capacity{resource=~\"$5.*\"}) * 100",
      "cluster_npu_utilisation":               "avg(npu_chip_info_utilization)",
      "cluster_total_npu_type_num":            "sum(kube_node_status_capacity{resource=~\"huawei.*\"}) by(resource) > 0",
      "cluster_total_npu_num":                 "sum(kube_node_status_capacity{resource=~\"huawei.*\"})",
      "cluster_file_filesystem_utilisation":   "(avg(node_filesystem_size_bytes{device=~\"MOSFS.*\"}) - avg(node_filesystem_avail_bytes{device=~\"MOSFS.*\"})) / avg(node_filesystem_size_bytes{device=~\"MOSFS.*\"}) * 100",
      "cluster_total_file_filesystem_bytes":   "avg(node_filesystem_size_bytes{device=~\"MOSFS.*\"})",
      "cluster_used_file_filesystem_bytes":    "avg(node_filesystem_size_bytes{device=~\"MOSFS.*\"}) - avg(node_filesystem_avail_bytes{device=~\"MOSFS.*\"})",
      "cluster_used_object_filesystem_bytes":  "minio_cluster_capacity_raw_total_bytes - minio_cluster_capacity_raw_free_bytes",
      "cluster_object_filesystem_utilisation": "(1 - minio_cluster_capacity_raw_free_bytes/minio_cluster_capacity_raw_total_bytes) * 100",
      "cluster_total_object_filesystem_bytes": "minio_cluster_capacity_raw_total_bytes",
      "cluster_network_receive_rate":          "instance:node_network_receive_bytes:rate:sum",
      "cluster_network_transmit_rate":         "instance:node_network_transmit_bytes:rate:sum",
      "cluster_component_health_status":       "kube_pod_container_status_ready{namespace!~\"default|hero-user\"}",

      "node_occupied_cpu_core":             "sum(cluster:namespace:pod_cpu:active:kube_pod_container_resource_requests{node=~\"$1\"}) by (node)",
      "node_total_cpu_core":                "sum(instance:node_num_cpu:sum{instance=~\"$1.*\"} ) by(instance)",
      "node_cpu_utilisation":               "instance:node_cpu_utilisation:rate5m{instance=~\"$1.*\"} * 100",
      "node_occupied_cpu_utilisation":      "sum(cluster:namespace:pod_cpu:active:kube_pod_container_resource_requests{node=~\"$1\"}) / sum(instance:node_num_cpu:sum{instance=~\"$1.*\"} ) * 100",
      "node_occupied_mem_utilisation":      "sum(kube_pod_status_phase{phase=\"Running\"} * on (pod) group_left() sum(kube_pod_container_resource_requests{node=~\"$1.*\",resource=\"memory\"}) by (pod)) / sum(node_memory_MemTotal_bytes{instance=~\"$1.*\"})  * 100",
	    "node_occupied_mem_bytes":            "sum(kube_pod_status_phase{phase=\"Running\"} * on (pod) group_left(node) sum by (pod,node) (kube_pod_container_resource_requests{node=~\"$1.*\",resource=\"memory\"})) by (node)",
      "node_total_mem_bytes":               "node_memory_MemTotal_bytes{instance=~\"$1.*\"}",
      "node_mem_utilisation":               "instance:node_memory_utilisation:ratio{instance=~\"$1.*\"} * 100",
      "node_occupied_gpu_type_num":         "sum(kube_pod_status_phase{phase=\"Running\"} * on (pod) group_left(namespace, resource) sum(kube_pod_container_resource_requests{resource=~\"nvidia.*\",node=~\"$1.*\"}) by (pod, resource)) by (resource)",
      "node_occupied_gpu_num":              "sum(kube_pod_status_phase{phase=\"Running\"} * on (pod) group_left(namespace, resource) sum(kube_pod_container_resource_requests{resource=~\"nvidia.*\",node=~\"$1.*\"}) by (pod, resource))",
      "node_occupied_gpu_utilisation":      "(sum(kube_pod_status_phase{phase=\"Running\"} * on (pod) group_left sum(kube_pod_container_resource_requests{resource=~\"nvidia.*\",node=~\"$1.*\"}) by (pod, resource))) / sum(kube_node_status_capacity{resource=~\"nvidia.*\",node=~\"$1.*\"}) * 100",
      "node_occupied_gpu_type_utilisation": "sum(kube_pod_status_phase{phase=\"Running\",namespace=\"hero-user\"} * on (pod) group_right sum(kube_pod_container_resource_requests{resource=~\"nvidia.*\",namespace=\"hero-user\",node=~\"$1.*\"}) by (pod,resource,node) > 0) by (resource,node)  / (sum(kube_node_status_capacity{resource=~\"nvidia.*\",node=~\"$1.*\"}) by(node,resource) > 0)  * 100",
      "node_occupied_gpu_type_time_hour":   "sum_over_time(sum by (node)(kube_pod_container_resource_requests{resource=~\"nvidia.*|huawei.*\",namespace=\"hero-user\",node=~\"$1.*\"}* on (pod) group_left sum by (pod) (kube_pod_status_phase{phase=\"Running\",namespace=\"hero-user\"}) > 0)[$6:]) * 30 / 3600",
      "node_total_gpu_type_num":            "sum(kube_node_status_capacity{resource=~\"nvidia.*\",node=~\"$1.*\"}) by(node,resource) > 0",
      "node_gpu_utilisation":               "avg(DCGM_FI_DEV_GPU_UTIL{Hostname=~\"$1.*\"})",
      "node_occupied_npu_type_num":         "sum(kube_pod_status_phase{phase=\"Running\"} * on (pod) group_left(namespace, resource) sum(kube_pod_container_resource_requests{resource=~\"huawei.*\",node=~\"$1.*\"}) by (pod, resource)) by (resource)",
      "node_occupied_npu_num":              "sum(kube_pod_status_phase{phase=\"Running\"} * on (pod) group_left(namespace, resource) sum(kube_pod_container_resource_requests{resource=~\"huawei.*\",node=~\"$1.*\"}) by (pod, resource))",
      "node_occupied_npu_utilisation":      "(sum(kube_pod_status_phase{phase=\"Running\"} * on (pod) group_left sum(kube_pod_container_resource_requests{resource=~\"huawei.*\",node=~\"$1.*\"}) by (pod, resource))) / sum(kube_node_status_capacity{resource=~\"huawei.*\",node=~\"$1.*\"}) * 100",
      "node_occupied_npu_type_utilisation": "sum(kube_pod_status_phase{phase=\"Running\"} * on (pod) group_left (node,resource) sum(kube_pod_container_resource_requests{resource=~\"huawei.*\",node=~\"$1.*\"}) by (pod, resource,node)) by(node,resource)  / sum(kube_node_status_capacity{resource=~\"huawei.*\",node=~\"$1.*\"}) by(node,resource) * 100",
      "node_npu_utilisation":               "sum(kube_pod_info{node=~\"$1.*\"} * on(pod) group_right(node) npu_chip_info_utilization) by (node)",
      "node_total_npu_type_num":            "sum(kube_node_status_capacity{resource=~\"huawei.*\",node=~\"$1.*\"}) by(node,resource) > 0",
      "node_network_receive_rate":          "instance:node_network_receive_bytes:rate:sum{instance=~\"$1.*\"}",
      "node_network_transmit_rate":         "instance:node_network_transmit_bytes:rate:sum{instance=~\"$1.*\"}",
      "node_disk_free_bytes":               "node_filesystem_free_bytes{mountpoint =\"/\",instance=~\"$1.*\"}",
      "node_disk_total_bytes":              "node_filesystem_size_bytes{mountpoint =\"/\",instance=~\"$1.*\"}",
      "node_disk_utilisation":              "((node_filesystem_size_bytes{mountpoint =\"/\",instance=~\"$1.*\"} - node_filesystem_free_bytes{mountpoint =\"/\",instance=~\"$1.*\"}) / node_filesystem_size_bytes{mountpoint =\"/\",instance=~\"$1.*\"}) * 100",
      "node_gpu_card_info":                 "DCGM_FI_DEV_FB_USED{Hostname=~\"$1.*\"}",

      "job_requests_successed_rate":     "sum(istio_requests_total{pod=~\"$2.*\", namespace=~\"$3.*\", response_code=\"200\"}) / sum(istio_requests_total{pod=~\"$2.*\", namespace=\"$3\"})  *100",
      "job_requests_total":              "sum(istio_requests_total{pod=~\"$2.*\", namespace=~\"$3.*\",response_code=~\"$4.*\"}) by (pod,response_code)",
      "job_response_certain_code_total": "sum(istio_requests_total{pod=~\"$2.*\", namespace=~\"$3.*\",response_code=~\"$4.*\"}) by (response_code)",
      "job_fs_read_usage":               "sum(irate(container_fs_reads_bytes_total{pod=~\"$2.*\"}[1m])) by (pod)",
      "job_fs_write_usage":              "sum(irate(container_fs_writes_bytes_total{pod=~\"$2.*\"}[1m])) by (pod)",
      "job_network_io_rec":              "sum(irate(container_network_receive_bytes_total{pod=~\"$2.*\"}[1m])) by (pod)",
      "job_network_io_trans":            "sum(irate(container_network_transmit_bytes_total{pod=~\"$2.*\"}[1m])) by (pod)",
      "job_mem_used":                    "sum(container_memory_working_set_bytes{pod=~\"$2.*\",container=\"\"}) by (pod)",
      "job_mem_all":                     "sum(kube_pod_container_resource_limits{pod=~\"$2.*\",resource=\"memory\"}) by (pod)",
      "job_mem_utilization":             "sum(container_memory_working_set_bytes{pod=~\"$2.*\",container=\"\"}) by (pod) / sum(kube_pod_container_resource_limits{pod=~\"$2.*\",resource=\"memory\"}) by (pod) * 100",
      "job_cpu_usage":                   "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_irate{pod=~\"$2.*\"}) by (pod) * 100",
      "job_npu_utilization":             "container_npu_utilization{pod_name=~\"$2.*\"}",
      "job_npu_mem_utilization":         "container_npu_used_memory{pod_name=~\"$2.*\"} / container_npu_total_memory{pod_name=~\"$2.*\"} * 100",
      "job_npu_mem_used":                "container_npu_used_memory{pod_name=~\"$2.*\"}",
      "job_npu_mem_all":                 "container_npu_total_memory{pod_name=~\"$2.*\"}",
      "job_gpu_mem_used":                "label_replace(kube_pod_status_phase{phase=\"Running\",pod=~\"$2.*\"} > 0,\"exported_pod\",\"$1\",\"pod\",\"(.*)\") * on (exported_pod) group_right () DCGM_FI_DEV_FB_USED{exported_pod=~\"$2.*\"}",
      "job_gpu_utilization":             "label_replace(kube_pod_status_phase{phase=\"Running\",pod=~\"$2.*\"} > 0,\"exported_pod\",\"$1\",\"pod\",\"(.*)\") * on (exported_pod) group_right () DCGM_FI_DEV_GPU_UTIL{exported_pod=~\"$2.*\",gpu=~\".*\"}",
      "job_gpu_mem_all":                 "label_replace(kube_pod_status_phase{phase=\"Running\",pod=~\"$2.*\"} > 0,\"exported_pod\",\"$1\",\"pod\",\"(.*)\") * on (exported_pod) group_right () (DCGM_FI_DEV_FB_FREE{exported_pod=~\"$2.*\"} + DCGM_FI_DEV_FB_USED{exported_pod=~\"$2.*\"})",
      "job_gpu_mem_utilization":         "(label_replace(kube_pod_status_phase{phase=\"Running\",pod=~\"$2.*\"} > 0,\"exported_pod\",\"$1\",\"pod\",\"(.*)\") * on (exported_pod) group_right () DCGM_FI_DEV_FB_USED{exported_pod=~\"$2.*\"}) / (DCGM_FI_DEV_FB_FREE{exported_pod=~\"$2.*\"}+DCGM_FI_DEV_FB_USED{exported_pod=~\"$2.*\"}) * 100",


      "node_used_cpu_utilisation":         "instance:node_cpu_utilisation:rate5m{instance=~\"$1.*\"} * 100",
      "node_used_mem_utilisation":         "sum(cluster:namespace:pod_memory:active:kube_pod_container_resource_requests{node=~\"$1\"}) / sum(node_memory_MemTotal_bytes{instance=~\"$1\"}) * 100",
      "node_occupied_gpu_num_utilisation": "(sum(kube_pod_status_phase{phase=\"Running\"} * on (pod) group_left sum(kube_pod_container_resource_requests{resource=~\"nvidia.*\",node=~\"$1.*\"}) by (pod, resource)))/ sum(kube_node_status_capacity{resource=~\"nvidia.*\",node=~\"$1.*\"}) * 100",
    }
  promQLRateTemplates:
    {
      "job_requests_rate": "sum(istio_requests_total{pod=~\"$2.*\", namespace=~\"$3.*\"})",
    }
grafana: 
  addr: 10.0.102.61
  port: 33814
  defaultUser: admin
  defaultPassword: admin
rocketmq:
  endpoints:
    - http://rmqnamesrv.basic-component.svc.cluster.local:9876
  taskGroup: file-proxy
  topic: file-proxy
  accessKey: ""
  secretKey: ""
cert:
  caCert: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURZekNDQWt1Z0F3SUJBZ0lDQm5vd0RRWUpLb1pJaHZjTkFRRUxCUUF3RnpFVk1CTUdBMVVFQ2hNTVlYVjAKYUMxaFpHRndkR1Z5TUNBWERUSXpNVEF3TnpBMk1EWXlOVm9ZRHpJeE1qTXhNREEzTURZd05qSTFXakFSTVE4dwpEUVlEVlFRS0V3WlRSVkpXUlZJd2dnRWlNQTBHQ1NxR1NJYjNEUUVCQVFVQUE0SUJEd0F3Z2dFS0FvSUJBUUM1ClJCWlRKNTRZNjU0c21hclJoRVVMOGhEVUpHMmR0VFU0WmtTdk5TaTdrMk1kRUtzRGJNd3JJNUdOTjluQjFJU3YKYWsvOWVHRUwvR3FrakNsN2dyVWJocVQxcWhNTUplSVdpcmFvQTMvSnlRMkFZVGwwUlFKOVdqMW1PbVM0STgzSgpFdnBZbTZFYWRMSUljNS9HaDkxNFlLZTB6bVorNUk4bW1kNWp6SndoV1ZJeWV4a2dJRWZUejE0TU8rTVRtdVlJCnZ0emhMTzFUcEx6U09sUjRWN2pnemlMM2ZHRDgxWEprSlB1RHNCcGNMd3BZZlBkeDY0eVExSTVoUER4RUN0S0sKTjhiR0c1TlhqdlB4a1c2L05OaHZNOVBEaUpSaVRuSnBNU3B2dldaV2hMYTliUnFxN2hOOG9tejlWSlpITmxMbgpuRkJCSFBYNEVtK2tpbDcyOG0wYkFnTUJBQUdqZ2J3d2dia3dEZ1lEVlIwUEFRSC9CQVFEQWdLRU1CMEdBMVVkCkpRUVdNQlFHQ0NzR0FRVUZCd01DQmdnckJnRUZCUWNEQVRBT0JnTlZIUTRFQndRRkFRSURCQVl3RUFZRFZSMGoKQkFrd0I0QUZBUUlEQkFVd1pnWURWUjBSQkY4d1hZSUpiRzlqWVd4b2IzTjBnaHBvWlhKdkxXRndhWE5sY25abApjaTVrWldaaGRXeDBMbk4yWTRJb2FHVnlieTFoY0dselpYSjJaWEl1WkdWbVlYVnNkQzV6ZG1NdVkyeDFjM1JsCmNpNXNiMk5oYkljRWZ3QUFBWWNFQ2dCbUxUQU5CZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFUeFg5NisxbXlxRXkKaDg1RkdvYTMvUjI1NEtOanh0TDNMZ3FYeGl1L0JIYzlNRG53UW1QUVB1cTFGalBablB1MGhlT0VmeVQwQ3ZuTgpVQU42Q2FtZEVUMkZoWW5EdU0rT3BnNjBZTG9EeFcwNmFFbE92ejIzeUNXTXZvUm9adnZBQ0ZBRDRJZ0JqZlJXCnR4OE1UOXFXb2xQQWZXY1JXVEpFNnhvbWpxR0hkRWdWbVArT1JsYm55cHZRbEFMVmJBUlRJT29RSkQxNFlzdjYKT2xDaGRaU0hnbmV1dXhDejhBN2ZhSWpmZnBrdnQ3WU1YMG9MdGxEaGE4QW1vTi9BRDdtWUQzYzNob0JXOXRHMAoxeTMxZTVDS21XZENjSFZlWmU4MUNxSWdFeU1JYjQzZnNONGUwV05MSjRVVW05eVB2L3pucE1ETDVhNFJUQU1uCjUrUit2OFV2VGc9PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
  caKey: 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
volcano:
  actions: "enqueue, allocate, backfill"
  tiers:
    - plugins:
      - name: priority
      - name: gang
      - name: conformance
    - plugins:
      - name: drf
      - name: predicates
      - name: nodeorder
      - name: proportion
        enableJobEnqueued: false
      - name: binpack
        arguments:
          binpack.weight: 10
          binpack.resources: nvidia.com/gpu,nvidia.com/nvidia-rtx-3090,nvidia.com/a100-sxm4-80gb,nvidia.com/a100-80gb-pcie,nvidia.com/nvidia-xp,huawei.com/Ascend910
          binpack.resources.nvidia.com/gpu: 10
          binpack.resources.nvidia.com/nvidia-rtx-3090: 10
          binpack.resources.nvidia.com/a100-sxm4-80gb: 10
          binpack.resources.nvidia.com/a100-80gb-pcie: 10
          binpack.resources.nvidia.com/nvidia-xp: 10
          binpack.resources.huawei.com/Ascend910: 10