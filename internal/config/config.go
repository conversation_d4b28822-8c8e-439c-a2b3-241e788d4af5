package config

import (
	"encoding/base64"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sync"
	"syscall"
	"time"

	"github.com/apache/rocketmq-client-go/v2"
	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/apache/rocketmq-client-go/v2/producer"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"github.com/pkg/errors"
	log "github.com/sirupsen/logrus"
	commonconfig "gitlab.bitahub.com/hero-os/hero-os-util/config"
	"golang.org/x/crypto/ssh"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/klog"
	"volcano.sh/apis/pkg/client/clientset/versioned"
	"volcano.sh/volcano/cmd/scheduler/app/options"
	"volcano.sh/volcano/pkg/scheduler/conf"
)

const (
	SuccessRequest = 0
	BadRequest     = 401
)

var (
	K8sClientSet     kubernetes.Interface
	karmadaClientSet dynamic.Interface
	VolcanoClientset *versioned.Clientset
	dynamicClient    dynamic.Interface
	config           *rest.Config
	Once             sync.Once
	VolConf          sync.Once
	VolOpt           sync.Once
	DyOnce           sync.Once
	kubeconfigOnce   sync.Once
)

type MqConfig struct {
	Endpoints []string `json:"endpoints,omitempty" yaml:"endpoints"`
	TaskGroup string   `json:"taskGroup,omitempty" yaml:"taskGroup"`
	Topic     string   `json:"topic,omitempty" yaml:"topic"`
	AccessKey string   `json:"accessKey"  yaml:"accessKey"`
	SecretKey string   `json:"secretKey"  yaml:"secretKey"`
}

type HTTPConfig struct {
	Addr string `yaml:"addr"`
	Port int    `yaml:"port"`
	TLS  bool   `yaml:"tls"`
}

type PrometheusConfig struct {
	Addr                string            `yaml:"addr"`
	Port                string            `yaml:"port"`
	NodePort            string            `yaml:"nodeport"`
	PromQLTemplates     map[string]string `yaml:"promQLTemplates"`
	PromQLRateTemplates map[string]string `yaml:"promQLRateTemplates"`
}

type KarmadaConfig struct {
	KubeConfig []string `yaml:"kubeconfig"`
}

type GrafanaConfig struct {
	Addr                string `yaml:"addr"`
	Port                string `yaml:"port"`
	DefaultuserUser     string `yaml:"defaultUser"`
	DefaultuserPassword string `yaml:"defaultPassword"`
}

type LokiConfig struct {
	Addr string `yaml:"addr"`
	Port int    `yaml:"port"`
}

func (h *HTTPConfig) GetURL() string {
	if h.TLS {
		return fmt.Sprintf("https://%s:%d", h.Addr, h.Port)
	}
	return fmt.Sprintf("http://%s:%d", h.Addr, h.Port)
}

func (h *HTTPConfig) GetAddress() string {
	return fmt.Sprintf("%s:%d", h.Addr, h.Port)
}

type TLSConfig struct {
	Cert string `yaml:"cert"`
	Key  string `yaml:"key"`
}

var (
	svcDataID = "/Users/<USER>/goRepo/resource-manager/configs/resource-manager-config.yaml"
	ecDataID  = "/Users/<USER>/goRepo/resource-manager/configs/cluster-env.yaml"
)

// 本地调试设置的配置文件路径,也可将svcDataId视作文件位置
// var (
// 	cfgFile1 string = "/root/dekai.hu/resource-manager/configs/resource-manager-config.yaml"
// )

var (
	cfg *commonconfig.Config
	SC  = &ServerConfig{}
	EC  = &EnvConfig{}
)

type ServerConfig struct {
	Version    string
	LogLevel   int                         `yaml:"logLevel"`
	HTTP       HTTPConfig                  `yaml:"http"`
	TLS        TLSConfig                   `yaml:"tls"`
	Prometheus PrometheusConfig            `yaml:"prometheus"`
	Loki       LokiConfig                  `yaml:"loki"`
	Grafana    GrafanaConfig               `yaml:"grafana"`
	Karmada    KarmadaConfig               `yaml:"karmada"`
	RocketMq   *MqConfig                   `json:"rocketmq,omitempty" yaml:"rocketmq"`
	VolcanoCf  conf.SchedulerConfiguration `yaml:"volcano"`
	Cert       CertInfo                    `json:"cert,omitempty" yaml:"cert"`
	SSH        SSH                         `json:"ssh,omitempty" yaml:"ssh"`
}

type SSH struct {
	User     string `json:"user,omitempty" yaml:"user"`
	Password string `json:"password,omitempty" yaml:"password"`
}

func NewSSHCongfig() *ssh.ClientConfig {
	return &ssh.ClientConfig{
		User: SC.SSH.User,
		Auth: []ssh.AuthMethod{
			ssh.Password(SC.SSH.Password),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // 忽略主机密钥
		Timeout:         30 * time.Second,
	}
}

type EnvConfig struct {
	MinioS3    *MinioConfig       `json:"s3,omitempty" yaml:"S3"  mapstructure:"S3"`
	MinioFS    *MinioConfig       `json:"fileSystem,omitempty" yaml:"FileSystem"  mapstructure:"FileSystem"`
	Cluster    *CLusterConfig     `json:"CLUSTER,omitempty" yaml:"CLUSTER"  mapstructure:"CLUSTER"`
	ClusterK8S *CLusterK8SSConfig `json:"CLUSTER_K8S,omitempty" yaml:"CLUSTER_K8S"  mapstructure:"CLUSTER_K8S"`
	ProductID  string             `json:"PRODUCT_UUID,omitempty" yaml:"PRODUCT_UUID"  mapstructure:"PRODUCT_UUID"`
}

type CLusterConfig struct {
	IP                       string `json:"IP,omitempty" yaml:"IP"`
	Port                     string `json:"API_PORT,omitempty" yaml:"API_PORT" mapstructure:"API_PORT"`
	CertificateAuthorityData string `json:"CERTIFICATE_AUTHORITY_DATA,omitempty" yaml:"CERTIFICATE_AUTHORITY_DATA" mapstructure:"CERTIFICATE_AUTHORITY_DATA"`
	ClientCertificateData    string `json:"CLIENT_CERTIFICATE_DATA,omitempty" yaml:"CLIENT_CERTIFICATE_DATA" mapstructure:"CLIENT_CERTIFICATE_DATA"`
	ClientKeyData            string `json:"CLIENT_KEY_DATA,omitempty" yaml:"CLIENT_KEY_DATA" mapstructure:"CLIENT_KEY_DATA"`
}

type CLusterK8SSConfig struct {
	IP                       string `json:"IP,omitempty" yaml:"IP"`
	Port                     string `json:"API_PORT,omitempty" yaml:"API_PORT" mapstructure:"API_PORT"`
	CertificateAuthorityData string `json:"CERTIFICATE_AUTHORITY_DATA,omitempty" yaml:"CERTIFICATE_AUTHORITY_DATA" mapstructure:"CERTIFICATE_AUTHORITY_DATA"`
	ClientCertificateData    string `json:"CLIENT_CERTIFICATE_DATA,omitempty" yaml:"CLIENT_CERTIFICATE_DATA" mapstructure:"CLIENT_CERTIFICATE_DATA"`
	ClientKeyData            string `json:"CLIENT_KEY_DATA,omitempty" yaml:"CLIENT_KEY_DATA" mapstructure:"CLIENT_KEY_DATA"`
	Token                    string `json:"TOKEN,omitempty" yaml:"TOKEN" mapstructure:"TOKEN"`
}

type CertInfo struct {
	CaCert string `json:"caCert,omitempty" yaml:"caCert"`
	CaKey  string `json:"caKey,omitempty" yaml:"caKey"`
}

type MinioConfig struct {
	URL       string `yaml:"URL"`
	HTTPS     bool   `yaml:"HTTPS"`
	AccessKey string `yaml:"ACCESS_KEY"  mapstructure:"ACCESS_KEY"`
	SecretKey string `yaml:"SECRET_KEY" mapstructure:"SECRET_KEY"`
}

func init() {
	cfg = commonconfig.NewConfig(commonconfig.SetConfig(
		commonconfig.SetNacosConfig(os.Getenv("NACOS_SERVER_URL"), os.Getenv("JASYPT_ENCRYPTOR_PASSWORD"), os.Getenv("INIT_MODE")),
		commonconfig.SetConfigSource(svcDataID, SC),
		commonconfig.SetConfigSource(ecDataID, EC),
	))

	if err := cfg.InitConfig(); err != nil {
		panic(err)
	}

	if err := SC.saveTLSConfig(); err != nil {
		panic(err)
	}
}

// save file
func (sc *ServerConfig) saveTLSConfig() error {

	if SC.HTTP.TLS {
		ca, _ := base64.StdEncoding.DecodeString(SC.Cert.CaCert)
		if err := save(SC.TLS.Cert, ca); err != nil {
			return err
		}

		key, _ := base64.StdEncoding.DecodeString(SC.Cert.CaKey)
		if err := save(SC.TLS.Key, key); err != nil {
			return err
		}
	}

	return nil
}

func saveToWriter(writer io.Writer, data []byte) error {

	_, err := writer.Write(data)
	return err
}

func save(fileName string, data []byte) (retErr error) {
	dir := filepath.Dir(fileName)
	if err := os.MkdirAll(dir, 0700); err != nil {
		return err
	}
	temp, err := os.CreateTemp(dir, filepath.Base(fileName))
	if err != nil {
		return err
	}

	err = os.Chmod(temp.Name(), 0777)
	if err != nil {
		return err
	}
	defer func() {
		temp.Close()
		if retErr != nil {
			if err := os.Remove(temp.Name()); err != nil {
				fmt.Printf("Error cleaning up temp file, err: %s", err.Error())
			}
		}
	}()

	err = saveToWriter(temp, data)
	if err != nil {
		return err
	}

	if err := temp.Close(); err != nil {
		return errors.Wrap(err, "error closing temp file")
	}

	// Handle situation where the configfile is a symlink
	cfgFile := fileName
	if f, err := os.Readlink(cfgFile); err == nil {
		cfgFile = f
	}

	// Try copying the current config file (if any) ownership and permissions
	copyFilePermissions(cfgFile, temp.Name())
	return os.Rename(temp.Name(), cfgFile)
}

func copyFilePermissions(src, dst string) {
	var (
		mode     os.FileMode = 0777
		uid, gid int
	)

	fi, err := os.Stat(src)
	if err != nil {
		return
	}
	if fi.Mode().IsRegular() {
		mode = fi.Mode()
	}
	if err := os.Chmod(dst, mode); err != nil {
		return
	}

	uid = int(fi.Sys().(*syscall.Stat_t).Uid)
	gid = int(fi.Sys().(*syscall.Stat_t).Gid)

	if uid > 0 && gid > 0 {
		_ = os.Chown(dst, uid, gid)
	}
}

func (sc *ServerConfig) GetLokiAddress() string {
	return fmt.Sprintf("http://%s:%d", sc.Loki.Addr, sc.Loki.Port)
}

func (sc *ServerConfig) GetPrometheusAddress() string {
	return fmt.Sprintf("http://%s:%s", sc.Prometheus.Addr, sc.Prometheus.Port)
}

func NewK8sConfig() *rest.Config {
	var err error
	//var config *rest.Config
	kubeconfigOnce.Do(func() {
		kubeconfig, ok := os.LookupEnv("kubeconfig")
		if ok {
			config, err = clientcmd.BuildConfigFromFlags("", kubeconfig)
		} else {
			config, err = rest.InClusterConfig()
		}
		if err != nil {
			log.Errorf("k8s build config errror:%s", err.Error())
		}
	})

	return config

}

func NewKarmadaConfig() *rest.Config {
	config := &rest.Config{
		Host: "https://" + EC.Cluster.IP + EC.Cluster.Port,
	}
	caData, err := base64.StdEncoding.DecodeString(EC.Cluster.CertificateAuthorityData)
	if err != nil {
		log.Errorf("karmada build config errror:%s", err.Error())
		return nil
	}
	config.TLSClientConfig.CAData = caData

	clientCertData, err := base64.StdEncoding.DecodeString(EC.Cluster.ClientCertificateData)
	if err != nil {
		log.Errorf("karmada build config errror:%s", err.Error())
		return nil
	}
	config.TLSClientConfig.CertData = clientCertData

	clientKeyData, err := base64.StdEncoding.DecodeString(EC.Cluster.ClientKeyData)
	if err != nil {
		log.Errorf("karmada build config errror:%s", err.Error())
		return nil
	}
	config.TLSClientConfig.KeyData = clientKeyData
	return config

}

func NewK8sClient() kubernetes.Interface {
	Once.Do(func() {
		K8sClientSet = kubernetes.NewForConfigOrDie(NewK8sConfig())
	})

	return K8sClientSet
}

func NewDynamicClient() dynamic.Interface {
	DyOnce.Do(func() {
		dynamicClient = dynamic.NewForConfigOrDie(NewK8sConfig())
	})

	return dynamicClient
}

func NewKarmadaClient() dynamic.Interface {
	if EC.ClusterK8S == nil {
		return nil
	}
	DyOnce.Do(func() {
		karmadaClientSet = dynamic.NewForConfigOrDie(NewKarmadaConfig())
	})

	return karmadaClientSet
}

func NewVolcanoClient() *versioned.Clientset {
	VolConf.Do(func() {
		var err error
		VolcanoClientset, err = versioned.NewForConfig(NewK8sConfig())
		if err != nil {
			log.Errorf("volcano new config errror:%s", err.Error())
		}
	})
	return VolcanoClientset
}

func InitVolcanoSchedulerOptions() {
	VolOpt.Do(func() {
		options.ServerOpts = &options.ServerOption{
			SchedulerNames:             []string{"volcano"},
			DefaultQueue:               "default",
			EnablePriorityClass:        true,
			MinNodesToFind:             int32(100),
			MinPercentageOfNodesToFind: int32(5),
			PercentageOfNodesToFind:    int32(100),
			NodeSelector:               []string{},
		}
		if len(options.ServerOpts.SchedulerConf) == 0 {
			log.Errorf("configuration 'volcano::schedulerConf' is missing or empty")
		}
	})
}

func NewMqClient() *rocketmq.Producer {
	p, err := rocketmq.NewProducer(
		producer.WithNameServer(SC.RocketMq.Endpoints),
		producer.WithGroupName(SC.RocketMq.TaskGroup),
		producer.WithCredentials(primitive.Credentials{
			AccessKey: SC.RocketMq.AccessKey,
			SecretKey: SC.RocketMq.SecretKey,
		}),
		producer.WithRetry(5),
		producer.WithSendMsgTimeout(3*time.Second),
		producer.WithQueueSelector(producer.NewHashQueueSelector()),
	)
	if err != nil {
		klog.Errorf("create producer error: %s", err.Error())
		return nil
	}
	err = p.Start()
	if err != nil {
		klog.Errorf("start producer error: %s", err.Error())
		return nil
	}
	return &p
}

func NewMinioS3Client() *minio.Client {
	minioClient, err := minio.New(EC.MinioS3.URL, &minio.Options{
		Creds:  credentials.NewStaticV4(EC.MinioS3.AccessKey, EC.MinioS3.SecretKey, ""),
		Secure: EC.MinioS3.HTTPS,
	})
	if err != nil {
		fmt.Println("Initial Error, ", err.Error())
	}
	return minioClient
}

func NewMinioFSClient() *minio.Client {
	minioClient, err := minio.New(EC.MinioFS.URL, &minio.Options{
		Creds:  credentials.NewStaticV4(EC.MinioFS.AccessKey, EC.MinioFS.SecretKey, ""),
		Secure: EC.MinioFS.HTTPS,
	})
	if err != nil {
		fmt.Println("Initial Error, ", err.Error())
	}
	return minioClient
}
